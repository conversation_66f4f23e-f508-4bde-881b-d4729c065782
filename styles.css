/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Bold & Vibrant Color Palette */
    --primary-color: #00a8ff;
    --primary-dark: #0084c7;
    --secondary-color: #e100ff;
    --accent-color: #ef4444;
    --success-color: #10b981;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --bg-dark: #050914;
    --bg-darker: #030712;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #00a8ff 0%, #e100ff 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Typography */
    --font-family: 'Inter', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--bg-dark) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Collapsed state using scroll-driven animations */
.header.scrolled {
    padding: var(--space-2) 0;
    background: rgba(5, 9, 20, 0.95) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.scrolled .header-content {
    padding: var(--space-2) 0;
}

/* Logo scaling on scroll */
.logo {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.scrolled .logo img {
    height: 32px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Nav menu adjustments for collapsed state */
.header.scrolled .nav-menu a {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-sm);
}

.header.scrolled .cta-button {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    gap: var(--space-8);
    align-items: center;
}

.nav-menu a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-family: var(--font-family);
    font-weight: 600;
    font-size: var(--font-size-base);
    letter-spacing: 0.025em;
    transition: all 0.3s ease;
    position: relative;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--border-radius-md);
}

.nav-menu a:hover {
    color: var(--white);
    text-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.logo img {
    height: 40px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
}

.mobile-menu-toggle {
    display: none;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    cursor: pointer;
}

/* CTA Button Styles */
.cta-button {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.cta-button.primary {
    background: var(--gradient-primary);
    color: var(--white);
    font-weight: 700;
    font-size: var(--font-size-lg);
    padding: var(--space-4) var(--space-8);
}

.cta-button.large {
    font-size: var(--font-size-xl);
    padding: var(--space-5) var(--space-10);
}

/* Hero Section */
.hero {
    padding: calc(80px + var(--space-20)) 0 var(--space-20);
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    color: var(--white);
    overflow: hidden;
    position: relative;
}

/* Space Dust Particles */
.hero::before,
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.hero::before {
    background-image: 
        radial-gradient(5px 5px at 20% 30%, #00a8ff, transparent),
        radial-gradient(3px 3px at 40% 70%, #e100ff, transparent),
        radial-gradient(2px 2px at 90% 40%, #f093fb, transparent),
        radial-gradient(4px 4px at 60% 10%, #00a8ff, transparent),
        radial-gradient(6px 6px at 80% 80%, #f5576c, transparent),
        radial-gradient(2px 2px at 10% 90%, #e100ff, transparent),
        radial-gradient(3px 3px at 70% 60%, #f093fb, transparent),
        radial-gradient(4px 4px at 30% 20%, #f5576c, transparent),
        radial-gradient(2px 2px at 50% 50%, #00a8ff, transparent),
        radial-gradient(3px 3px at 85% 15%, #e100ff, transparent),
        radial-gradient(2px 2px at 75% 45%, #ffffff, transparent),
        radial-gradient(1px 1px at 35% 25%, #00a8ff, transparent),
        radial-gradient(2px 2px at 65% 75%, #f093fb, transparent),
        radial-gradient(1px 1px at 15% 60%, #00a8ff, transparent),
        radial-gradient(5px 5px at 95% 20%, #e100ff, transparent),
        radial-gradient(2px 2px at 25% 85%, #f093fb, transparent),
        radial-gradient(3px 3px at 55% 30%, #f5576c, transparent),
        radial-gradient(1px 1px at 80% 65%, #ffffff, transparent),
        radial-gradient(4px 4px at 5% 45%, #00a8ff, transparent),
        radial-gradient(2px 2px at 45% 5%, #e100ff, transparent),
        radial-gradient(3px 3px at 90% 75%, #f093fb, transparent),
        radial-gradient(1px 1px at 70% 85%, #f5576c, transparent),
        radial-gradient(6px 6px at 15% 15%, #00a8ff, transparent),
        radial-gradient(2px 2px at 85% 35%, #e100ff, transparent),
        radial-gradient(1px 1px at 60% 90%, #ffffff, transparent),
        radial-gradient(3px 3px at 95% 60%, #f093fb, transparent),
        radial-gradient(4px 4px at 25% 40%, #f5576c, transparent),
        radial-gradient(1px 1px at 5% 25%, #00a8ff, transparent),
        radial-gradient(2px 2px at 75% 15%, #e100ff, transparent),
        radial-gradient(5px 5px at 45% 75%, #f093fb, transparent);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: float 15s ease-in-out infinite;
    filter: drop-shadow(0 0 3px rgba(0, 168, 255, 0.5)) drop-shadow(0 0 6px rgba(225, 0, 255, 0.3));
}

.hero::after {
    background-image:
        radial-gradient(3px 3px at 25% 80%, #f093fb, transparent),
        radial-gradient(4px 4px at 75% 25%, #e100ff, transparent),
        radial-gradient(2px 2px at 45% 35%, #00a8ff, transparent),
        radial-gradient(3px 3px at 65% 85%, #f5576c, transparent),
        radial-gradient(4px 4px at 15% 45%, #f093fb, transparent),
        radial-gradient(2px 2px at 95% 70%, #00a8ff, transparent),
        radial-gradient(3px 3px at 35% 95%, #e100ff, transparent),
        radial-gradient(4px 4px at 55% 5%, #f5576c, transparent),
        radial-gradient(2px 2px at 5% 25%, #f093fb, transparent),
        radial-gradient(3px 3px at 85% 55%, #e100ff, transparent),
        radial-gradient(2px 2px at 45% 15%, #ffffff, transparent),
        radial-gradient(1px 1px at 25% 45%, #00a8ff, transparent),
        radial-gradient(3px 3px at 75% 65%, #f5576c, transparent),
        radial-gradient(1px 1px at 90% 30%, #f093fb, transparent),
        radial-gradient(5px 5px at 10% 70%, #e100ff, transparent),
        radial-gradient(2px 2px at 30% 10%, #00a8ff, transparent),
        radial-gradient(4px 4px at 85% 90%, #f5576c, transparent),
        radial-gradient(1px 1px at 60% 55%, #ffffff, transparent),
        radial-gradient(3px 3px at 20% 65%, #f093fb, transparent),
        radial-gradient(6px 6px at 90% 10%, #e100ff, transparent),
        radial-gradient(2px 2px at 40% 80%, #00a8ff, transparent),
        radial-gradient(1px 1px at 70% 20%, #f5576c, transparent),
        radial-gradient(4px 4px at 95% 45%, #f093fb, transparent),
        radial-gradient(2px 2px at 15% 35%, #e100ff, transparent),
        radial-gradient(3px 3px at 50% 70%, #00a8ff, transparent),
        radial-gradient(1px 1px at 80% 50%, #ffffff, transparent),
        radial-gradient(5px 5px at 35% 25%, #f5576c, transparent),
        radial-gradient(2px 2px at 65% 95%, #f093fb, transparent),
        radial-gradient(1px 1px at 10% 15%, #e100ff, transparent),
        radial-gradient(3px 3px at 55% 40%, #00a8ff, transparent);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: float 18s ease-in-out infinite reverse;
    filter: drop-shadow(0 0 4px rgba(240, 147, 251, 0.5)) drop-shadow(0 0 8px rgba(245, 87, 108, 0.3));
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 1;
    }
    25% {
        transform: translateY(-20px) translateX(10px);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) translateX(-5px);
        opacity: 0.9;
    }
    75% {
        transform: translateY(-15px) translateX(8px);
        opacity: 0.7;
    }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    opacity: 0.9;
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    align-items: flex-start;
}

.trust-indicator {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    font-weight: 500;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.video-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    aspect-ratio: 16/9;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    padding: 8px;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(0, 168, 255, 0.3),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
    filter: drop-shadow(0 0 20px rgba(225, 0, 255, 0.2));
}

.video-container::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    z-index: -1;
    filter: blur(8px);
    opacity: 0.7;
}

.video-container iframe {
    position: relative;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: calc(var(--border-radius-xl) - 8px);
    background: var(--bg-darker);
    z-index: 1;
    box-sizing: border-box;
    overflow: hidden;
}

.screenshot-placeholder {
    background: rgba(255, 255, 255, 0.1);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-xl);
    padding: var(--space-16);
    text-align: center;
    width: 100%;
    max-width: 500px;
    aspect-ratio: 16/10;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-4);
    overflow: hidden;
}

.screenshot-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    max-width: 100%;
    max-height: 100%;
}

.screenshot-placeholder i {
    font-size: 4rem;
    opacity: 0.7;
}

.screenshot-placeholder p {
    font-size: var(--font-size-lg);
    font-weight: 600;
    opacity: 0.8;
}

/* Section Titles */
.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    text-align: center;
    margin-bottom: var(--space-16);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Benefits Section */
.benefits {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
}

.benefit-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.benefit-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    color: var(--text-dark);
}

.benefit-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Features Section */
.features {
    padding: var(--space-20) 0;
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    position: relative;
    overflow: hidden;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    position: relative;
    z-index: 2;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: var(--space-6);
    border-radius: var(--border-radius-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.feature-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 168, 255, 0.3);
}

.feature-card i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--space-4);
    filter: drop-shadow(0 0 8px rgba(0, 168, 255, 0.5));
}

.feature-card h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--white);
}

.feature-card p {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* Screenshots Section */
.screenshots {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.screenshots-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-16);
}

.screenshot-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    align-items: center;
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
}

.screenshot-item:nth-child(even) {
    direction: rtl;
}

.screenshot-item:nth-child(even) > * {
    direction: ltr;
}

.screenshot-item .screenshot-placeholder {
    background: var(--gray-100);
    border: 2px dashed var(--gray-300);
    color: var(--text-light);
    aspect-ratio: 16/10;
    overflow: hidden;
    padding: var(--space-4);
    position: relative;
}

.screenshot-item .screenshot-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    max-width: 100%;
    max-height: 100%;
}

/* Image Loading States */
.image-loader {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.image-loader img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.5s ease;
    position: absolute;
    top: 0;
    left: 0;
}

.image-loader.loaded img {
    opacity: 1;
}

.image-loading,
.image-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--text-light);
    transition: opacity 0.3s ease;
}

.image-loader.loaded .image-loading,
.image-loader.error .image-loading {
    opacity: 0;
    pointer-events: none;
}

.image-error {
    opacity: 0;
    pointer-events: none;
}

.image-loader.error .image-error {
    opacity: 1;
    pointer-events: auto;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-300);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.image-loading span,
.image-error span {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.image-error i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-2);
    opacity: 0.5;
}

/* Progressive image loading with blur effect */
.image-loader::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        var(--gray-100) 0%,
        var(--gray-200) 25%,
        var(--gray-100) 50%,
        var(--gray-200) 75%,
        var(--gray-100) 100%
    );
    background-size: 200% 200%;
    animation: shimmer 2s ease-in-out infinite;
    opacity: 1;
    transition: opacity 0.5s ease;
    z-index: 1;
}

.image-loader.loaded::before {
    opacity: 0;
}

@keyframes shimmer {
    0% {
        background-position: 200% 200%;
    }
    100% {
        background-position: -200% -200%;
    }
}

.screenshot-content h4 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    color: var(--text-dark);
}

.screenshot-content p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    padding: var(--space-20) 0;
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    position: relative;
    overflow: hidden;
}

.pricing-subtitle {
    text-align: center;
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--space-8);
}

.pricing-toggle-container {
    text-align: center;
    margin-bottom: var(--space-12);
}

.pricing-toggle {
    display: inline-flex;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--space-1);
    margin-bottom: var(--space-3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.toggle-option {
    background: transparent;
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.toggle-option:hover {
    color: var(--white);
}

.toggle-option.active {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    box-shadow: 0 4px 16px rgba(0, 168, 255, 0.3);
}

.toggle-note {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    position: relative;
    z-index: 2;
}

.pricing-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
    border-color: rgba(0, 168, 255, 0.5);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.15);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-secondary);
    color: var(--white);
    padding: var(--space-2) var(--space-6);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.pricing-header h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--space-4);
}

.price {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: var(--space-4) 0;
}

.price-row {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--space-1);
}

.currency {
    font-size: var(--font-size-xl);
    color: #4facfe;
}

.amount {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: #4facfe;
}

.period {
    font-size: var(--font-size-base);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
}

.billing-note {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
    display: block;
    margin-top: var(--space-2);
    text-align: center;
}

.trial-info {
    display: block;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-top: var(--space-2);
}

.partnership-pricing {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: var(--space-4) 0;
}

.partnership-pricing .pricing-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: #4facfe;
    margin-bottom: var(--space-1);
}

.partnership-pricing .pricing-subtitle {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

.features-list {
    list-style: none;
    margin-bottom: var(--space-8);
}

.features-list li {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
    color: var(--white);
}

.features-list i {
    color: var(--success-color);
    font-size: var(--font-size-sm);
    filter: drop-shadow(0 0 4px rgba(16, 185, 129, 0.5));
}

.pricing-card .cta-button {
    display: block;
    margin: 0 auto;
    text-align: center;
    width: auto;
    max-width: none;
    white-space: nowrap;
}

/* Final CTA Section */
.final-cta {
    padding: var(--space-20) 0;
    background: var(--gradient-primary);
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    margin-bottom: var(--space-6);
}

.cta-content p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    opacity: 0.9;
}

.cta-note {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin-top: var(--space-4);
}

/* Final CTA Button White Outline */
.final-cta .cta-button.primary {
    border: 2px solid white;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3), 
                0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Footer */
.footer {
    background: 
        radial-gradient(500px 400px at top left, rgba(0, 168, 255, 0.15), transparent),
        radial-gradient(600px 500px at bottom right, rgba(225, 0, 255, 0.12), transparent),
        var(--bg-dark);
    color: var(--white);
    padding: var(--space-16) 0 var(--space-8);
    position: relative;
    overflow: hidden;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
    position: relative;
    z-index: 2;
}

.footer-logo img {
    height: 32px;
    width: auto;
    max-width: 150px;
    margin-bottom: var(--space-2);
    object-fit: contain;
}

.footer-logo p {
    color: rgba(255, 255, 255, 0.8);
}

.footer-links {
    display: flex;
    gap: var(--space-6);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--white);
    text-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: var(--space-8);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    position: relative;
    z-index: 2;
}

/* Animation Classes */
.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-up {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.slide-in-left.animate,
.slide-in-right.animate,
.slide-in-up.animate {
    opacity: 1;
    transform: translate(0);
}

/* FAQ Section */
.faq {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.faq-grid {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.faq-item {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.faq-question:hover {
    background: var(--gray-50);
}

.faq-question h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.4;
    flex: 1;
    padding-right: var(--space-4);
}

.faq-icon {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background: var(--gray-50);
}

.faq-item.active .faq-answer {
    max-height: 500px;
    padding: 0 var(--space-6) var(--space-6);
}

.faq-answer p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    padding-top: var(--space-2);
}

/* Mobile Logo Styles */
.mobile-logo {
    display: none;
}

/* Subtitle Display Control */
.mobile-subtitle {
    display: none;
}

.desktop-subtitle {
    display: block;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-4);
    }
    
    /* Hide Header on Mobile */
    .header {
        display: none;
    }
    
    /* Mobile Logo */
    .mobile-logo {
        display: block;
        text-align: center;
        margin-bottom: var(--space-8);
    }
    
    .mobile-logo img {
        height: 60px;
        width: auto;
        max-width: 250px;
        object-fit: contain;
    }
    
    /* Subtitle Mobile Control */
    .desktop-subtitle {
        display: none;
    }
    
    .mobile-subtitle {
        display: block;
    }
    
    /* Hero Section Mobile */
    .hero {
        padding: var(--space-4) 0 var(--space-16);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--space-10);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
        margin-bottom: var(--space-4);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
        margin-bottom: var(--space-6);
        line-height: 1.5;
    }
    
    .hero-cta {
        align-items: center;
        gap: var(--space-3);
    }
    
    .trust-indicator {
        text-align: center;
        font-size: var(--font-size-sm);
    }
    
    /* Video Container Mobile */
    .video-container {
        max-width: 100%;
        margin: 0 auto;
        padding: 0;
        background: transparent;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 
                    0 20px 60px rgba(0, 0, 0, 0.2);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
    }
    
    .video-container::before {
        display: none;
    }
    
    .video-container iframe {
        border-radius: var(--border-radius-lg);
        box-sizing: border-box;
        overflow: hidden;
        max-width: 100%;
        max-height: 100%;
    }
    
    /* Section Titles */
    .section-title {
        font-size: var(--font-size-3xl);
        margin-bottom: var(--space-12);
        line-height: 1.2;
    }
    
    /* Benefits Section Mobile */
    .benefits {
        padding: var(--space-16) 0;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: var(--space-5);
    }
    
    .benefit-card {
        padding: var(--space-6);
        margin: 0 var(--space-2);
    }
    
    .benefit-card h3 {
        font-size: var(--font-size-lg);
    }
    
    .benefit-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
        margin-bottom: var(--space-4);
    }
    
    /* Features Section Mobile */
    .features {
        padding: var(--space-16) 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .feature-card {
        padding: var(--space-5);
        text-align: center;
    }
    
    .feature-card i {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--space-3);
    }
    
    .feature-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--space-2);
    }
    
    /* Screenshots Section Mobile */
    .screenshots {
        padding: var(--space-16) 0;
    }
    
    .screenshot-item {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
        padding: var(--space-6);
        margin: 0 var(--space-2);
    }
    
    .screenshot-item:nth-child(even) {
        direction: ltr;
    }
    
    .screenshot-item .screenshot-placeholder {
        padding: var(--space-5);
        aspect-ratio: 16/10.5;
    }
    
    .screenshot-item .screenshot-placeholder img {
        width: 98%;
        height: 98%;
        object-fit: contain;
    }
    
    .screenshot-content h4 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--space-3);
    }
    
    /* Mobile image optimization */
    .image-loader {
        min-height: 250px; /* Updated to match mobile image height */
    }
    
    .loading-spinner {
        width: 30px;
        height: 30px;
        border-width: 2px;
    }
    
    .image-loading span,
    .image-error span {
        font-size: var(--font-size-xs);
    }
    
    /* Reduce shimmer animation intensity on mobile */
    .image-loader::before {
        animation-duration: 1.5s;
    }
    
    /* Pricing Section Mobile */
    .pricing {
        padding: var(--space-16) 0;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--space-5);
    }
    
    .pricing-card {
        padding: var(--space-6);
        margin: 0 var(--space-2);
        transform: none !important;
    }
    
    .pricing-card.featured {
        transform: none !important;
        scale: 1 !important;
    }
    
    /* Mobile Popular Badge */
    .popular-badge {
        top: -15px;
        padding: var(--space-1) var(--space-4);
        font-size: var(--font-size-xs);
        border-radius: var(--border-radius-md);
    }
    
    .pricing-header h3 {
        font-size: var(--font-size-xl);
    }
    
    .currency, .amount {
        font-size: var(--font-size-2xl);
    }
    
    .amount {
        font-size: var(--font-size-3xl);
    }
    
    /* FAQ Section Mobile */
    .faq {
        padding: var(--space-16) 0;
    }
    
    .faq-grid {
        margin: 0 var(--space-2);
    }
    
    .faq-question {
        padding: var(--space-5);
    }
    
    .faq-question h4 {
        font-size: var(--font-size-base);
        padding-right: var(--space-3);
    }
    
    /* Footer Mobile */
    .footer {
        padding: var(--space-12) 0 var(--space-6);
    }
    
    .footer-content {
        flex-direction: column;
        gap: var(--space-5);
        text-align: center;
    }
    
    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--space-4);
    }
    
    /* Button Adjustments */
    .cta-button {
        padding: var(--space-3) var(--space-6);
        font-size: var(--font-size-base);
        min-height: 44px;
        border-radius: var(--border-radius-lg);
    }
    
    .cta-button.primary {
        padding: var(--space-4) var(--space-8);
        font-size: var(--font-size-lg);
        min-height: 48px;
    }
    
    .cta-button.large {
        font-size: var(--font-size-xl);
        padding: var(--space-5) var(--space-10);
        min-height: 52px;
    }
    
    /* Remove hover effects on touch devices */
    .benefit-card:hover,
    .feature-card:hover,
    .pricing-card:hover,
    .screenshot-item:hover,
    .faq-item:hover {
        transform: none;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-3);
    }
    
    /* Hero Section Small Mobile */
    .hero {
        padding: calc(80px + var(--space-12)) 0 var(--space-12);
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.1;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    /* Section adjustments */
    .benefits, .features, .screenshots, .pricing, .faq {
        padding: var(--space-12) 0;
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--space-8);
    }
    
    /* Card adjustments */
    .benefit-card, .pricing-card, .screenshot-item {
        margin: 0 var(--space-1);
        padding: var(--space-5);
    }
    
    .benefit-card h3, .screenshot-content h4 {
        font-size: var(--font-size-base);
    }
    
    .benefit-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    /* Pricing adjustments */
    .pricing-header h3 {
        font-size: var(--font-size-lg);
    }
    
    .currency {
        font-size: var(--font-size-lg);
    }
    
    .amount {
        font-size: var(--font-size-2xl);
    }
    
    /* Button adjustments */
    .cta-button {
        padding: var(--space-3) var(--space-5);
        font-size: var(--font-size-sm);
        width: 100%;
        max-width: 280px;
    }
    
    .cta-button.primary {
        padding: var(--space-4) var(--space-6);
        font-size: var(--font-size-base);
    }
    
    .cta-button.large {
        font-size: var(--font-size-lg);
        padding: var(--space-4) var(--space-8);
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .benefit-card:hover,
    .feature-card:hover,
    .pricing-card:hover,
    .screenshot-item:hover,
    .faq-item:hover,
    .nav-menu a:hover,
    .cta-button:hover {
        transform: none;
        box-shadow: inherit;
    }
    
    /* Make touch targets larger */
    .faq-question,
    .toggle-option,
    .cta-button {
        min-height: 44px;
    }
}
